# core/views.py

import uuid
import logging
import asyncio
from datetime import datetime, timedelta
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated, AllowAny

# 确保引入了所有需要的模型和序列化器
from .models import User, Room, RoomParticipant, RoomState
from .serializers import UserSerializer, RoomSerializer, CustomTokenObtainPairSerializer
from .services.room_manager import room_manager
from .exceptions import handle_room_exception, RoomSystemException
from events.models import EventTemplate, EventStep # 确保引入 EventStep
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken

logger = logging.getLogger(__name__)


class APIRootView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"message": "Welcome to the Tuanzi API!"})

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [AllowAny]

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

class HealthCheckView(APIView):
    """
    健康检查端点
    用于验证用户认证状态和会话有效性
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """返回用户认证状态"""
        logger.debug(f"健康检查请求，用户: {request.user.username}")

        return Response({
            'status': 'ok',
            'authenticated': True,
            'user': request.user.username,
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)


class RoomCreateView(APIView):
    """
    使用手写的APIView来创建房间，以完全控制创建和返回的逻辑。
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """创建新房间 - 使用新的房间管理器"""
        logger.info(f"收到创建房间请求，用户: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试创建房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        template_id = request.data.get('template_id')
        if not template_id:
            logger.warning(f"用户 {request.user.username} 创建房间时未提供模板ID")
            return Response({"error": "Template ID is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            template = EventTemplate.objects.get(id=template_id)
            logger.info(f"找到模板: {template.name} (ID: {template_id})")
        except EventTemplate.DoesNotExist:
            logger.warning(f"用户 {request.user.username} 尝试使用不存在的模板ID: {template_id}")
            return Response({"error": "Template not found."}, status=status.HTTP_404_NOT_FOUND)

        host = request.user

        # 订阅等级检查逻辑
        if host.subscription_level == host.SUBSCRIPTION_FREE:
            premium_steps = template.steps.filter(step_type__in=EventStep.PREMIUM_STEP_TYPES)
            if premium_steps.exists():
                premium_step_names = [step.get_step_type_display() for step in premium_steps]
                logger.info(f"免费用户 {host.username} 尝试使用付费模板，包含付费环节: {premium_step_names}")
                return Response({
                    "error": "此模板包含付费专属环节，请升级到Pro版本以使用。",
                    "premium_steps": premium_step_names,
                    "upgrade_required": True
                }, status=status.HTTP_403_FORBIDDEN)

        try:
            logger.info(f"开始为用户 {host.username} 创建房间，使用模板: {template.name}")

            # 使用同步方法调用房间管理器
            new_room = room_manager.create_room_sync(host=host, template_id=template_id)
            logger.info(f"房间创建成功: {new_room.room_code}, 房主: {host.username}")

            # 序列化并返回房间数据
            serializer = RoomSerializer(new_room)
            logger.info(f"返回房间数据给用户 {host.username}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except RoomSystemException as e:
            logger.error(f"房间系统异常: {e.message}, 用户: {host.username}")
            return Response(handle_room_exception(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"创建房间时发生未预期错误: {e}, 用户: {host.username}", exc_info=True)
            return Response({
                "error": "创建房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class JoinRoomView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """用户加入房间 - 使用新的房间管理器"""
        logger.info(f"收到加入房间请求，用户: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试加入房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        room_code = request.data.get('room_code')
        if not room_code:
            logger.warning(f"用户 {request.user.username} 加入房间时未提供房间代码")
            return Response(
                {"error": "Room code is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        logger.info(f"用户 {user.username} 尝试加入房间: {room_code}")

        try:
            # 使用同步方法调用房间管理器
            success, message = room_manager.join_room_sync(room_code, user)

            if success:
                logger.info(f"用户 {user.username} 成功加入房间 {room_code}")
                # 获取房间信息并返回
                room = room_manager.get_room_sync(room_code)
                serializer = RoomSerializer(room)
                return Response({
                    "message": message,
                    "room": serializer.data
                }, status=status.HTTP_200_OK)
            else:
                logger.warning(f"用户 {user.username} 加入房间 {room_code} 失败: {message}")
                # 根据错误消息确定HTTP状态码
                if "不存在" in message:
                    status_code = status.HTTP_404_NOT_FOUND
                elif "已满" in message or "过期" in message or "不允许" in message or "已关闭" in message:
                    status_code = status.HTTP_403_FORBIDDEN
                else:
                    status_code = status.HTTP_400_BAD_REQUEST

                return Response({"error": message}, status=status_code)

        except RoomSystemException as e:
            logger.error(f"房间系统异常: {e.message}, 用户: {user.username}")
            return Response(handle_room_exception(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"加入房间时发生未预期错误: {e}, 用户: {user.username}", exc_info=True)
            return Response({
                "error": "加入房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RoomDetailView(APIView):
    """
    获取房间详情
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, room_code, *args, **kwargs):
        """获取房间详情"""
        logger.info(f"收到获取房间详情请求，房间: {room_code}, 用户: {request.user.username}")

        try:
            # 获取房间信息
            room = room_manager.get_room_sync(room_code)

            # 序列化并返回房间数据
            serializer = RoomSerializer(room)
            logger.info(f"返回房间 {room_code} 详情给用户 {request.user.username}")
            return Response(serializer.data, status=status.HTTP_200_OK)

        except RoomSystemException as e:
            logger.error(f"获取房间详情时发生房间系统异常: {e.message}, 房间: {room_code}")
            return Response(handle_room_exception(e), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"获取房间详情时发生未预期错误: {e}, 房间: {room_code}", exc_info=True)
            return Response({
                "error": "获取房间详情时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SubscriptionManagementView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user
        return Response({
            'current_level': user.subscription_level,
            'username': user.username,
            'subscription_info': {
                'Free': {'max_participants': 10, 'duration_hours': 2},
                'Pro': {'max_participants': 500, 'duration_hours': 24},
                'Max': {'max_participants': 2000, 'duration_hours': 72},
            }
        })

    def post(self, request, *args, **kwargs):
        target_level = request.data.get('target_level')
        is_debug = request.data.get('is_debug', False)
        if target_level not in [User.SUBSCRIPTION_FREE, User.SUBSCRIPTION_PRO, User.SUBSCRIPTION_MAX]:
            return Response(
                {"error": "Invalid subscription level"},
                status=status.HTTP_400_BAD_REQUEST
            )
        user = request.user
        if is_debug and hasattr(request, 'META') and request.META.get('HTTP_X_DEBUG_MODE'):
            user.subscription_level = target_level
            user.save()
            refresh = RefreshToken.for_user(user)
            return Response({
                'message': f'Debug: Subscription level changed to {target_level}',
                'new_level': target_level,
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh)
            })
        return Response({
            'message': 'Payment integration not implemented yet',
        }, status=status.HTTP_501_NOT_IMPLEMENTED)


class ScheduleRoomView(APIView):
    """
    预约房间API端点
    允许用户预约未来某个时间的游戏房间
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """创建预约房间"""
        logger.info(f"收到预约房间请求，用户: {request.user.username}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试预约房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        # 获取请求参数
        name = request.data.get('name')
        template_id = request.data.get('template_id')
        scheduled_start_time_str = request.data.get('scheduled_start_time')
        duration_hours = request.data.get('duration_hours')

        # 参数验证
        if not all([name, template_id, scheduled_start_time_str, duration_hours]):
            missing_fields = []
            if not name: missing_fields.append('name')
            if not template_id: missing_fields.append('template_id')
            if not scheduled_start_time_str: missing_fields.append('scheduled_start_time')
            if not duration_hours: missing_fields.append('duration_hours')

            logger.warning(f"用户 {request.user.username} 预约房间时缺少必要参数: {missing_fields}")
            return Response({
                "error": "Missing required fields",
                "missing_fields": missing_fields
            }, status=status.HTTP_400_BAD_REQUEST)

        # 解析预约时间
        try:
            # 尝试解析ISO格式的时间字符串
            scheduled_start_time = parse_datetime(scheduled_start_time_str)
            if not scheduled_start_time:
                # 如果parse_datetime失败，尝试其他格式
                scheduled_start_time = datetime.fromisoformat(scheduled_start_time_str.replace('Z', '+00:00'))

            # 确保时间是timezone-aware的
            if scheduled_start_time.tzinfo is None:
                scheduled_start_time = timezone.make_aware(scheduled_start_time)

        except (ValueError, TypeError) as e:
            logger.warning(f"用户 {request.user.username} 提供的预约时间格式无效: {scheduled_start_time_str}")
            return Response({
                "error": "Invalid scheduled_start_time format. Use ISO format (e.g., '2023-12-25T14:30:00Z')"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证预约时间不能是过去时间
        if scheduled_start_time <= timezone.now():
            logger.warning(f"用户 {request.user.username} 尝试预约过去的时间: {scheduled_start_time}")
            return Response({
                "error": "Scheduled time must be in the future"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证持续时长
        try:
            duration_hours = int(duration_hours)
            if duration_hours <= 0 or duration_hours > 72:  # 最大72小时
                raise ValueError("Duration must be between 1 and 72 hours")
        except (ValueError, TypeError):
            logger.warning(f"用户 {request.user.username} 提供的持续时长无效: {duration_hours}")
            return Response({
                "error": "Invalid duration_hours. Must be an integer between 1 and 72"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证模板存在性和权限
        try:
            from .models import TemplateManager
            template_obj, template_type = TemplateManager.get_template_by_id(template_id, request.user)

            if not template_obj:
                logger.warning(f"用户 {request.user.username} 尝试使用不存在或无权限的模板ID: {template_id}")
                return Response({"error": "Template not found or access denied."}, status=status.HTTP_404_NOT_FOUND)

            logger.info(f"找到模板: {template_obj.name} (ID: {template_id}, Type: {template_type})")
        except Exception as e:
            logger.error(f"验证模板时发生错误: {e}")
            return Response({"error": "Template validation failed."}, status=status.HTTP_400_BAD_REQUEST)

        host = request.user

        # 订阅等级检查
        if host.subscription_level == host.SUBSCRIPTION_FREE:
            if template_type == 'user':
                # 用户模板：检查EventStep
                premium_steps = template_obj.steps.filter(step_type__in=EventStep.PREMIUM_STEP_TYPES)
                if premium_steps.exists():
                    premium_step_names = [step.get_step_type_display() for step in premium_steps]
                    logger.info(f"免费用户 {host.username} 尝试预约使用付费模板，包含付费环节: {premium_step_names}")
                    return Response({
                        "error": "此模板包含付费专属环节，请升级到Pro版本以使用。",
                        "premium_steps": premium_step_names,
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)
            elif template_type == 'system':
                # 系统模板：检查模板本身的订阅要求
                if not template_obj.is_accessible_by_user(host):
                    logger.info(f"免费用户 {host.username} 尝试使用需要更高订阅等级的系统模板: {template_obj.name}")
                    return Response({
                        "error": f"此模板需要 {template_obj.get_required_subscription_display()} 订阅等级。",
                        "required_subscription": template_obj.required_subscription,
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)

        try:
            logger.info(f"开始为用户 {host.username} 创建预约房间，使用模板: {template_obj.name}")

            # 创建预约房间
            room = self._create_scheduled_room(
                host=host,
                name=name,
                template_obj=template_obj,
                template_type=template_type,
                scheduled_start_time=scheduled_start_time,
                duration_hours=duration_hours
            )

            logger.info(f"预约房间创建成功: {room.room_code}, 房主: {host.username}, 预约时间: {scheduled_start_time}")

            # 序列化并返回房间数据
            serializer = RoomSerializer(room)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"创建预约房间时发生错误: {e}, 用户: {host.username}", exc_info=True)
            return Response({
                "error": "创建预约房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _create_scheduled_room(self, host, name, template_obj, template_type, scheduled_start_time, duration_hours):
        """
        创建预约房间的内部方法
        """
        from django.db import transaction
        from .models import TemplateManager

        with transaction.atomic():
            # 生成唯一房间号
            while True:
                room_code = uuid.uuid4().hex.upper()[:6]
                if not Room.objects.filter(room_code=room_code).exists():
                    break

            # 创建预约房间
            # 对于系统模板，event_template字段设为None，对于用户模板，设为对应的EventTemplate
            event_template = template_obj if template_type == 'user' else None

            room = Room.objects.create(
                room_code=room_code,
                host=host,
                event_template=event_template,
                status=RoomState.SCHEDULED,
                scheduled_start_time=scheduled_start_time,
                duration_hours=duration_hours
            )

            # 设置订阅限制
            room.set_limits_by_subscription(host)

            # 设置预约房间的过期时间
            room.set_scheduled_expiry()
            room.save()

            # 将房主添加为参与者
            RoomParticipant.objects.create(
                room=room,
                user=host,
                role=RoomParticipant.ROLE_HOST,
                state='JOINED'
            )

            # 复制模板步骤到房间
            TemplateManager.copy_template_steps_to_room(template_obj, template_type, room)

            return room


class CalendarDataView(APIView):
    """
    日历数据API端点
    提供日历界面所需的预约房间数据
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """获取日历预约数据"""
        logger.info(f"收到日历数据请求，用户: {request.user.username}")

        # 获取查询参数
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        month_str = request.GET.get('month')  # 格式: YYYY-MM

        try:
            # 确定查询的时间范围
            if month_str:
                # 按月查询
                year, month = map(int, month_str.split('-'))
                start_date = timezone.datetime(year, month, 1)
                if month == 12:
                    end_date = timezone.datetime(year + 1, 1, 1)
                else:
                    end_date = timezone.datetime(year, month + 1, 1)
                start_date = timezone.make_aware(start_date)
                end_date = timezone.make_aware(end_date)
            elif start_date_str and end_date_str:
                # 按日期范围查询
                start_date = parse_datetime(start_date_str)
                end_date = parse_datetime(end_date_str)
                if not start_date or not end_date:
                    raise ValueError("Invalid date format")
                if start_date.tzinfo is None:
                    start_date = timezone.make_aware(start_date)
                if end_date.tzinfo is None:
                    end_date = timezone.make_aware(end_date)
            else:
                # 默认查询当前月份
                now = timezone.now()
                start_date = timezone.datetime(now.year, now.month, 1)
                if now.month == 12:
                    end_date = timezone.datetime(now.year + 1, 1, 1)
                else:
                    end_date = timezone.datetime(now.year, now.month + 1, 1)
                start_date = timezone.make_aware(start_date)
                end_date = timezone.make_aware(end_date)

        except (ValueError, TypeError) as e:
            logger.warning(f"用户 {request.user.username} 提供的日期参数无效: {e}")
            return Response({
                "error": "Invalid date parameters. Use 'month' (YYYY-MM) or 'start_date' and 'end_date' (ISO format)"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 查询指定时间范围内的预约房间
            scheduled_rooms = Room.objects.filter(
                status=RoomState.SCHEDULED,
                scheduled_start_time__gte=start_date,
                scheduled_start_time__lt=end_date
            ).select_related('host', 'event_template').order_by('scheduled_start_time')

            # 构建响应数据
            reservations = []
            for room in scheduled_rooms:
                # 计算结束时间
                end_time = room.scheduled_start_time + timedelta(hours=room.duration_hours)

                reservation_data = {
                    'id': room.id,
                    'room_code': room.room_code,
                    'name': room.event_template.name if room.event_template else 'Unknown Event',
                    'host_username': room.host.username,
                    'start_time': room.scheduled_start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'duration_hours': room.duration_hours,
                    'max_participants': room.max_participants,
                    'current_participants': room.get_participant_count(),
                    'template_id': room.event_template.id if room.event_template else None,
                    'template_name': room.event_template.name if room.event_template else None,
                    'created_at': room.created_at.isoformat(),
                }
                reservations.append(reservation_data)

            logger.info(f"返回 {len(reservations)} 个预约记录给用户 {request.user.username}")

            return Response({
                'reservations': reservations,
                'query_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'total_count': len(reservations)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取日历数据时发生错误: {e}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "获取日历数据时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RoomTemplateListView(APIView):
    """
    房间模板列表API
    提供高性能的模板查询，支持缓存
    统一返回系统模板和用户模板
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """获取可用的房间模板列表"""
        logger.info(f"收到房间模板列表请求，用户: {request.user.username}")

        try:
            from .models import TemplateManager

            # 使用模板管理器获取用户可用的所有模板
            template_list = TemplateManager.get_available_templates_for_user(request.user)

            logger.info(f"返回 {len(template_list)} 个模板给用户 {request.user.username}")
            logger.debug(f"模板列表: {[t['name'] for t in template_list]}")

            return Response({
                'templates': template_list,
                'total_count': len(template_list)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取房间模板时发生错误: {e}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "获取房间模板时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
