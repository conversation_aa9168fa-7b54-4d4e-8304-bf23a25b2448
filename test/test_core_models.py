"""
全面的核心模型测试

测试core.models中的所有模型，包括：
- User模型：订阅等级、权限、用户组
- Room模型：状态管理、生命周期、限制
- RoomParticipant模型：角色权限、得分管理
- SystemTemplate模型：模板管理、权限检查
"""

import pytest
from datetime import datetime, timedelta
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from core.models import (
    User, Room, RoomParticipant, SystemTemplate, TemplateManager,
    RoomState, UserState
)
from events.models import EventTemplate, EventStep

User = get_user_model()


class UserModelTest(TestCase):
    """测试User模型的功能"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建用户组
        self.regular_group = Group.objects.get_or_create(name='Regular Users')[0]
        self.staff_group = Group.objects.get_or_create(name='Staff')[0]
        self.admin_group = Group.objects.get_or_create(name='Administrators')[0]
        self.super_admin_group = Group.objects.get_or_create(name='Super Administrators')[0]
    
    def test_user_creation_with_defaults(self):
        """测试用户创建时的默认值"""
        user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.assertEqual(user.subscription_level, User.SUBSCRIPTION_FREE)
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
    
    def test_user_subscription_levels(self):
        """测试用户订阅等级"""
        # 测试Free用户
        free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        self.assertEqual(free_user.subscription_level, User.SUBSCRIPTION_FREE)
        
        # 测试Pro用户
        pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        self.assertEqual(pro_user.subscription_level, User.SUBSCRIPTION_PRO)
        
        # 测试Max用户
        max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )
        self.assertEqual(max_user.subscription_level, User.SUBSCRIPTION_MAX)
    
    def test_user_group_methods(self):
        """测试用户组相关方法"""
        user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # 测试初始状态
        self.assertFalse(user.is_in_group('Staff'))
        self.assertFalse(user.is_administrator())
        self.assertFalse(user.is_staff_member())
        self.assertEqual(user.get_user_role(), 'user')
        
        # 添加到Staff组
        user.groups.add(self.staff_group)
        self.assertTrue(user.is_in_group('Staff'))
        self.assertTrue(user.is_staff_member())
        self.assertEqual(user.get_user_role(), 'staff')
        
        # 添加到管理员组
        user.groups.add(self.admin_group)
        self.assertTrue(user.is_administrator())
        self.assertEqual(user.get_user_role(), 'admin')
        
        # 添加到超级管理员组
        user.groups.add(self.super_admin_group)
        self.assertEqual(user.get_user_role(), 'super_admin')
    
    def test_user_str_representation(self):
        """测试用户字符串表示"""
        user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        expected = "testuser (Pro)"
        self.assertEqual(str(user), expected)


class RoomModelTest(TestCase):
    """测试Room模型的功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.template = EventTemplate.objects.create(
            name='Test Template',
            creator=self.user
        )
        
        EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
    
    def test_room_creation_with_defaults(self):
        """测试房间创建时的默认值"""
        room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            event_template=self.template
        )
        
        self.assertEqual(room.status, RoomState.OPEN)
        self.assertEqual(room.max_participants, 10)
        self.assertEqual(room.duration_hours, 2)
        self.assertIsNotNone(room.created_at)
        self.assertIsNotNone(room.last_activity_at)
    
    def test_room_subscription_limits(self):
        """测试根据订阅等级设置房间限制"""
        room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            event_template=self.template
        )
        
        # 测试Free用户限制
        free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        room.set_limits_by_subscription(free_user)
        self.assertEqual(room.max_participants, 10)
        self.assertEqual(room.duration_hours, 2)
        
        # 测试Pro用户限制
        pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        room.set_limits_by_subscription(pro_user)
        self.assertEqual(room.max_participants, 500)
        self.assertEqual(room.duration_hours, 24)
        
        # 测试Max用户限制
        max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )
        room.set_limits_by_subscription(max_user)
        self.assertEqual(room.max_participants, 2000)
        self.assertEqual(room.duration_hours, 72)
    
    def test_room_scheduled_functionality(self):
        """测试预约房间功能"""
        future_time = timezone.now() + timedelta(hours=1)
        
        room = Room.objects.create(
            room_code='SCHED01',
            host=self.user,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=future_time,
            duration_hours=2
        )
        
        # 测试设置过期时间
        room.set_scheduled_expiry()
        expected_expiry = future_time + timedelta(hours=2)
        self.assertEqual(room.expires_at, expected_expiry)
        
        # 测试是否准备就绪
        self.assertFalse(room.is_scheduled_ready())
        
        # 设置为过去时间
        past_time = timezone.now() - timedelta(minutes=1)
        room.scheduled_start_time = past_time
        room.save()
        self.assertTrue(room.is_scheduled_ready())
    
    def test_room_participant_management(self):
        """测试房间参与者管理"""
        room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            event_template=self.template
        )
        
        participant = User.objects.create_user(
            username='participant',
            password='testpass123'
        )
        
        # 测试添加参与者
        room_participant = room.add_participant(participant)
        self.assertEqual(room_participant.user, participant)
        self.assertEqual(room_participant.role, RoomParticipant.ROLE_PARTICIPANT)
        self.assertTrue(room_participant.is_active)
        
        # 测试获取参与者
        participants = room.get_participants()
        self.assertIn(participant, participants)
        
        # 测试参与者数量
        self.assertEqual(room.get_participant_count(), 1)
        
        # 测试重复添加同一用户
        duplicate_participant = room.add_participant(participant)
        self.assertEqual(duplicate_participant, room_participant)
        self.assertEqual(room.get_participant_count(), 1)
    
    def test_room_capacity_and_expiry(self):
        """测试房间容量和过期检查"""
        room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            event_template=self.template,
            max_participants=2
        )
        
        # 测试房间未满时可以加入
        self.assertTrue(room.can_join())
        self.assertFalse(room.is_full())
        
        # 添加参与者直到满员
        for i in range(2):
            user = User.objects.create_user(
                username=f'user{i}',
                password='testpass123'
            )
            room.add_participant(user)
        
        # 测试房间已满
        self.assertTrue(room.is_full())
        self.assertFalse(room.can_join())
        
        # 测试过期检查
        room.expires_at = timezone.now() - timedelta(hours=1)
        room.save()
        self.assertTrue(room.is_expired())
        self.assertFalse(room.can_join())
    
    def test_room_str_representation(self):
        """测试房间字符串表示"""
        room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            event_template=self.template,
            status=RoomState.IN_PROGRESS
        )
        
        expected = "Room TEST01 [活动中] (Test Template)"
        self.assertEqual(str(room), expected)
        
        # 测试预约房间的字符串表示
        scheduled_time = timezone.now() + timedelta(hours=1)
        scheduled_room = Room.objects.create(
            room_code='SCHED01',
            host=self.user,
            event_template=self.template,
            status=RoomState.SCHEDULED,
            scheduled_start_time=scheduled_time
        )
        
        # 字符串应该包含预约时间
        room_str = str(scheduled_room)
        self.assertIn('预约:', room_str)
        self.assertIn('SCHED01', room_str)


class RoomParticipantModelTest(TestCase):
    """测试RoomParticipant模型的功能"""

    def setUp(self):
        """设置测试数据"""
        self.host = User.objects.create_user(
            username='host',
            password='testpass123'
        )

        self.participant = User.objects.create_user(
            username='participant',
            password='testpass123'
        )

        self.template = EventTemplate.objects.create(
            name='Test Template',
            creator=self.host
        )

        self.room = Room.objects.create(
            room_code='TEST01',
            host=self.host,
            event_template=self.template
        )

    def test_room_participant_creation(self):
        """测试房间参与者创建"""
        participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.participant,
            role=RoomParticipant.ROLE_PARTICIPANT
        )

        self.assertEqual(participant.room, self.room)
        self.assertEqual(participant.user, self.participant)
        self.assertEqual(participant.role, RoomParticipant.ROLE_PARTICIPANT)
        self.assertEqual(participant.score, 0)
        self.assertEqual(participant.state, UserState.JOINED)
        self.assertTrue(participant.is_active)
        self.assertIsNone(participant.left_at)
        self.assertEqual(participant.custom_data, {})

    def test_room_participant_roles(self):
        """测试房间参与者角色"""
        # 测试普通参与者
        participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.participant,
            role=RoomParticipant.ROLE_PARTICIPANT
        )

        self.assertFalse(participant.can_manage_room())
        self.assertFalse(participant.can_control_game())

        # 测试协管员
        participant.role = RoomParticipant.ROLE_MODERATOR
        participant.save()

        self.assertTrue(participant.can_manage_room())
        self.assertFalse(participant.can_control_game())

        # 测试房主
        participant.role = RoomParticipant.ROLE_HOST
        participant.save()

        self.assertTrue(participant.can_manage_room())
        self.assertTrue(participant.can_control_game())

    def test_room_participant_score_management(self):
        """测试房间参与者得分管理"""
        participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.participant
        )

        # 测试初始得分
        self.assertEqual(participant.score, 0)

        # 测试增加得分
        participant.add_score(10)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 10)

        # 测试再次增加得分
        participant.add_score(5)
        participant.refresh_from_db()
        self.assertEqual(participant.score, 15)

        # 测试重置得分
        participant.reset_score()
        participant.refresh_from_db()
        self.assertEqual(participant.score, 0)

    def test_room_participant_unique_constraint(self):
        """测试房间参与者唯一性约束"""
        # 创建第一个参与者记录
        RoomParticipant.objects.create(
            room=self.room,
            user=self.participant
        )

        # 尝试创建重复记录应该失败
        with self.assertRaises(IntegrityError):
            RoomParticipant.objects.create(
                room=self.room,
                user=self.participant
            )

    def test_room_participant_str_representation(self):
        """测试房间参与者字符串表示"""
        participant = RoomParticipant.objects.create(
            room=self.room,
            user=self.participant,
            role=RoomParticipant.ROLE_MODERATOR
        )

        expected = "participant in Room TEST01 (协管员)"
        self.assertEqual(str(participant), expected)


class SystemTemplateModelTest(TestCase):
    """测试SystemTemplate模型的功能"""

    def setUp(self):
        """设置测试数据"""
        self.free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )

        self.pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )

        self.max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )

    def test_system_template_creation(self):
        """测试系统模板创建"""
        template = SystemTemplate.objects.create(
            name='Test System Template',
            description='A test system template',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {'type': 'GAME_PICTIONARY', 'duration': 300},
                    {'type': 'FREE_CHAT', 'duration': 180}
                ]
            }
        )

        self.assertEqual(template.name, 'Test System Template')
        self.assertTrue(template.is_active)
        self.assertEqual(template.required_subscription, SystemTemplate.SUBSCRIPTION_FREE)
        self.assertIsNotNone(template.created_at)

    def test_system_template_access_control(self):
        """测试系统模板访问控制"""
        # 创建不同订阅等级要求的模板
        free_template = SystemTemplate.objects.create(
            name='Free Template',
            description='Free template',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE
        )

        pro_template = SystemTemplate.objects.create(
            name='Pro Template',
            description='Pro template',
            required_subscription=SystemTemplate.SUBSCRIPTION_PRO
        )

        max_template = SystemTemplate.objects.create(
            name='Max Template',
            description='Max template',
            required_subscription=SystemTemplate.SUBSCRIPTION_MAX
        )

        # 测试Free用户访问权限
        self.assertTrue(free_template.is_accessible_by_user(self.free_user))
        self.assertFalse(pro_template.is_accessible_by_user(self.free_user))
        self.assertFalse(max_template.is_accessible_by_user(self.free_user))

        # 测试Pro用户访问权限
        self.assertTrue(free_template.is_accessible_by_user(self.pro_user))
        self.assertTrue(pro_template.is_accessible_by_user(self.pro_user))
        self.assertFalse(max_template.is_accessible_by_user(self.pro_user))

        # 测试Max用户访问权限
        self.assertTrue(free_template.is_accessible_by_user(self.max_user))
        self.assertTrue(pro_template.is_accessible_by_user(self.max_user))
        self.assertTrue(max_template.is_accessible_by_user(self.max_user))

    def test_system_template_inactive_access(self):
        """测试非活跃系统模板的访问"""
        template = SystemTemplate.objects.create(
            name='Inactive Template',
            description='Inactive template',
            is_active=False,
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE
        )

        # 即使订阅等级足够，非活跃模板也不能访问
        self.assertFalse(template.is_accessible_by_user(self.free_user))
        self.assertFalse(template.is_accessible_by_user(self.pro_user))
        self.assertFalse(template.is_accessible_by_user(self.max_user))

    def test_system_template_get_steps(self):
        """测试获取系统模板步骤"""
        steps_config = [
            {'type': 'GAME_PICTIONARY', 'duration': 300},
            {'type': 'FREE_CHAT', 'duration': 180}
        ]

        template = SystemTemplate.objects.create(
            name='Test Template',
            description='Test template',
            template_config={'steps': steps_config}
        )

        self.assertEqual(template.get_steps(), steps_config)

        # 测试没有步骤配置的情况
        empty_template = SystemTemplate.objects.create(
            name='Empty Template',
            description='Empty template'
        )

        self.assertEqual(empty_template.get_steps(), [])

    def test_system_template_str_representation(self):
        """测试系统模板字符串表示"""
        template = SystemTemplate.objects.create(
            name='Test Template',
            description='Test template'
        )

        expected = "[系统] Test Template"
        self.assertEqual(str(template), expected)


class TemplateManagerTest(TestCase):
    """测试TemplateManager的功能"""

    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )

        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='System Template',
            description='System template',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {'type': 'GAME_PICTIONARY', 'duration': 300}
                ]
            }
        )

        # 创建用户模板
        self.user_template = EventTemplate.objects.create(
            name='User Template',
            description='User template',
            creator=self.user
        )

        EventStep.objects.create(
            template=self.user_template,
            order=1,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=180,
            name='Chat Step'
        )

    def test_get_available_templates_for_user(self):
        """测试获取用户可用的所有模板"""
        templates = TemplateManager.get_available_templates_for_user(self.user)

        # 应该包含系统模板和用户模板
        self.assertEqual(len(templates), 2)

        # 检查系统模板
        system_template_data = next(
            (t for t in templates if t['type'] == 'system'), None
        )
        self.assertIsNotNone(system_template_data)
        self.assertEqual(system_template_data['name'], 'System Template')
        self.assertEqual(system_template_data['id'], f'system_{self.system_template.id}')

        # 检查用户模板
        user_template_data = next(
            (t for t in templates if t['type'] == 'user'), None
        )
        self.assertIsNotNone(user_template_data)
        self.assertEqual(user_template_data['name'], 'User Template')
        self.assertEqual(user_template_data['id'], f'user_{self.user_template.id}')
        self.assertEqual(len(user_template_data['steps']), 1)

    def test_get_template_by_id_system_template(self):
        """测试通过ID获取系统模板"""
        # 测试新格式ID
        template_id = f'system_{self.system_template.id}'
        template, template_type = TemplateManager.get_template_by_id(template_id, self.user)

        self.assertEqual(template, self.system_template)
        self.assertEqual(template_type, 'system')

        # 测试无效ID
        invalid_template, invalid_type = TemplateManager.get_template_by_id('system_999', self.user)
        self.assertIsNone(invalid_template)
        self.assertIsNone(invalid_type)

    def test_get_template_by_id_user_template(self):
        """测试通过ID获取用户模板"""
        # 测试新格式ID
        template_id = f'user_{self.user_template.id}'
        template, template_type = TemplateManager.get_template_by_id(template_id, self.user)

        self.assertEqual(template, self.user_template)
        self.assertEqual(template_type, 'user')

        # 测试兼容旧格式ID（纯数字）
        template, template_type = TemplateManager.get_template_by_id(str(self.user_template.id), self.user)
        self.assertEqual(template, self.user_template)
        self.assertEqual(template_type, 'user')

        # 测试数字ID
        template, template_type = TemplateManager.get_template_by_id(self.user_template.id, self.user)
        self.assertEqual(template, self.user_template)
        self.assertEqual(template_type, 'user')

    def test_get_template_by_id_access_control(self):
        """测试模板访问控制"""
        other_user = User.objects.create_user(
            username='otheruser',
            password='testpass123'
        )

        # 其他用户不能访问当前用户的模板
        template_id = f'user_{self.user_template.id}'
        template, template_type = TemplateManager.get_template_by_id(template_id, other_user)

        self.assertIsNone(template)
        self.assertIsNone(template_type)

        # 但可以访问系统模板
        system_template_id = f'system_{self.system_template.id}'
        template, template_type = TemplateManager.get_template_by_id(system_template_id, other_user)

        self.assertEqual(template, self.system_template)
        self.assertEqual(template_type, 'system')

    def test_get_template_by_id_invalid_format(self):
        """测试无效格式的模板ID"""
        invalid_ids = [
            'invalid_format',
            'system_abc',
            'user_xyz',
            '',
            None
        ]

        for invalid_id in invalid_ids:
            template, template_type = TemplateManager.get_template_by_id(invalid_id, self.user)
            self.assertIsNone(template, f"Should return None for invalid ID: {invalid_id}")
            self.assertIsNone(template_type, f"Should return None for invalid ID: {invalid_id}")

    def test_system_template_subscription_filtering(self):
        """测试系统模板按订阅等级过滤"""
        # 创建需要Pro订阅的系统模板
        pro_template = SystemTemplate.objects.create(
            name='Pro Template',
            description='Pro template',
            required_subscription=SystemTemplate.SUBSCRIPTION_PRO
        )

        # Free用户应该只能看到Free模板
        free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )

        templates = TemplateManager.get_available_templates_for_user(free_user)
        system_templates = [t for t in templates if t['type'] == 'system']

        # 应该只包含Free模板，不包含Pro模板
        template_names = [t['name'] for t in system_templates]
        self.assertIn('System Template', template_names)
        self.assertNotIn('Pro Template', template_names)

        # Pro用户应该能看到所有模板
        templates = TemplateManager.get_available_templates_for_user(self.user)
        system_templates = [t for t in templates if t['type'] == 'system']
        template_names = [t['name'] for t in system_templates]

        self.assertIn('System Template', template_names)
        self.assertIn('Pro Template', template_names)
