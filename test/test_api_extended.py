"""
扩展的API端点测试

包含更多边界条件、错误处理、权限验证等测试用例，补充现有API测试的不足
"""

import pytest
import json
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.cache import cache

from core.models import Room, RoomParticipant, RoomState, UserState, SystemTemplate
from events.models import EventTemplate, EventStep

User = get_user_model()


@pytest.mark.api
class TestAuthenticationAPIExtended:
    """扩展的认证API测试"""
    
    def setup_method(self):
        """设置测试数据"""
        self.client = APIClient()
        cache.clear()  # 清除缓存
    
    def teardown_method(self):
        """清理测试数据"""
        User.objects.filter(username__startswith='test_').delete()
    
    def test_registration_with_invalid_data(self):
        """测试无效数据注册"""
        invalid_data_sets = [
            {},  # 空数据
            {'username': ''},  # 空用户名
            {'password': ''},  # 空密码
            {'username': 'test_user'},  # 缺少密码
            {'password': 'testpass123'},  # 缺少用户名
            {'username': 'a', 'password': '123'},  # 密码太短
            {'username': 'test user', 'password': 'testpass123'},  # 用户名包含空格
        ]
        
        for invalid_data in invalid_data_sets:
            response = self.client.post('/api/register/', invalid_data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_registration_with_special_characters(self):
        """测试特殊字符用户名注册"""
        special_usernames = [
            'test@user',
            'test.user',
            'test-user',
            'test_user123',
            'TestUser',
        ]
        
        for username in special_usernames:
            data = {'username': username, 'password': 'testpass123'}
            response = self.client.post('/api/register/', data)
            # 某些特殊字符可能被允许，某些可能不被允许
            assert response.status_code in [status.HTTP_201_CREATED, status.HTTP_400_BAD_REQUEST]
    
    def test_login_with_invalid_credentials(self):
        """测试无效凭据登录"""
        # 创建测试用户
        user = User.objects.create_user(
            username='test_login_user',
            password='correctpass123'
        )
        
        invalid_credentials = [
            {'username': 'test_login_user', 'password': 'wrongpass'},
            {'username': 'nonexistent_user', 'password': 'anypass'},
            {'username': '', 'password': 'correctpass123'},
            {'username': 'test_login_user', 'password': ''},
            {},
        ]
        
        for credentials in invalid_credentials:
            response = self.client.post('/api/token/', credentials)
            assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_token_refresh_with_invalid_token(self):
        """测试无效token刷新"""
        invalid_tokens = [
            {'refresh': 'invalid_token'},
            {'refresh': ''},
            {},
            {'refresh': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid'},
        ]
        
        for token_data in invalid_tokens:
            response = self.client.post('/api/token/refresh/', token_data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_protected_endpoint_with_expired_token(self):
        """测试过期token访问受保护端点"""
        # 创建用户
        user = User.objects.create_user(
            username='test_expired_user',
            password='testpass123'
        )
        
        # 创建一个很快过期的token（这里我们模拟过期）
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # 使用token访问受保护端点
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.get('/api/room-templates/')

        # 正常情况下应该成功（token还没过期）
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_401_UNAUTHORIZED]


@pytest.mark.api
class TestRoomAPIExtended:
    """扩展的房间API测试"""
    
    def setup_method(self):
        """设置测试数据"""
        self.client = APIClient()
        cache.clear()
        
        # 创建测试用户
        self.host = User.objects.create_user(
            username='test_host',
            password='testpass123',
            subscription_level='Pro'
        )
        
        self.free_user = User.objects.create_user(
            username='test_free_user',
            password='testpass123',
            subscription_level='Free'
        )
        
        self.max_user = User.objects.create_user(
            username='test_max_user',
            password='testpass123',
            subscription_level='Max'
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='Extended Test Template',
            creator=self.host
        )
        
        EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
    
    def teardown_method(self):
        """清理测试数据"""
        Room.objects.filter(room_code__startswith='TEST').delete()
        EventTemplate.objects.filter(name__contains='Extended Test').delete()
        User.objects.filter(username__startswith='test_').delete()
    
    def authenticate_user(self, user):
        """认证用户"""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_create_room_without_authentication(self):
        """测试未认证用户创建房间"""
        data = {'template_id': self.template.id}
        response = self.client.post('/api/rooms/create/', data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_create_room_with_invalid_template(self):
        """测试使用无效模板创建房间"""
        self.authenticate_user(self.host)
        
        invalid_template_ids = [
            999999,  # 不存在的ID
            -1,      # 负数ID
            'invalid',  # 非数字ID
            '',      # 空字符串
        ]
        
        for template_id in invalid_template_ids:
            data = {'template_id': template_id}
            response = self.client.post('/api/rooms/create/', data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_create_room_with_other_users_template(self):
        """测试使用其他用户的模板创建房间"""
        # 创建另一个用户的模板
        other_user = User.objects.create_user(
            username='test_other_user',
            password='testpass123'
        )
        
        other_template = EventTemplate.objects.create(
            name='Other User Template',
            creator=other_user
        )
        
        self.authenticate_user(self.host)
        data = {'template_id': other_template.id}
        response = self.client.post('/api/rooms/create/', data)

        # 应该失败，因为用户不能使用其他用户的模板
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_join_room_with_invalid_code(self):
        """测试使用无效房间代码加入房间"""
        self.authenticate_user(self.free_user)
        
        invalid_codes = [
            'INVALID',
            '123456',
            '',
            'TOOLONG123',
            'short',
        ]
        
        for code in invalid_codes:
            data = {'room_code': code}
            response = self.client.post('/api/rooms/join/', data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_join_full_room(self):
        """测试加入已满的房间"""
        # 创建一个容量为1的房间
        room = Room.objects.create(
            room_code='FULL01',
            host=self.host,
            event_template=self.template,
            max_participants=1
        )
        
        # 添加一个参与者使房间满员
        room.add_participant(self.free_user)
        
        # 尝试让另一个用户加入
        self.authenticate_user(self.max_user)
        data = {'room_code': 'FULL01'}
        response = self.client.post('/api/rooms/join/', data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'full' in response.data.get('error', '').lower()
    
    def test_join_expired_room(self):
        """测试加入已过期的房间"""
        from django.utils import timezone
        from datetime import timedelta
        
        # 创建一个已过期的房间
        room = Room.objects.create(
            room_code='EXPIRED01',
            host=self.host,
            event_template=self.template,
            expires_at=timezone.now() - timedelta(hours=1)
        )
        
        self.authenticate_user(self.free_user)
        data = {'room_code': 'EXPIRED01'}
        response = self.client.post('/api/rooms/join/', data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_get_room_details_unauthorized(self):
        """测试未授权获取房间详情"""
        room = Room.objects.create(
            room_code='PRIVATE01',
            host=self.host,
            event_template=self.template
        )
        
        # 不认证直接访问
        response = self.client.get(f'/api/rooms/{room.room_code}/')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_subscription_limits_enforcement(self):
        """测试订阅限制强制执行"""
        # Free用户尝试创建超出限制的房间
        self.authenticate_user(self.free_user)
        
        # 创建一个需要Pro订阅的模板
        pro_template = EventTemplate.objects.create(
            name='Pro Template',
            creator=self.free_user
        )
        
        # 添加高级步骤类型
        EventStep.objects.create(
            template=pro_template,
            order=1,
            step_type=EventStep.STEP_POLL,  # 高级功能
            duration=300
        )
        
        data = {'template_id': pro_template.id}
        response = self.client.post('/api/rooms/create/', data)
        
        # 可能会成功创建，但应该有限制
        if response.status_code == status.HTTP_201_CREATED:
            room_code = response.data['room_code']
            room = Room.objects.get(room_code=room_code)
            # 检查是否应用了Free用户的限制
            assert room.max_participants <= 10
            assert room.duration_hours <= 2
    
    def test_concurrent_room_creation(self):
        """测试并发房间创建"""
        self.authenticate_user(self.host)
        
        # 快速创建多个房间
        responses = []
        for i in range(5):
            data = {'template_id': self.template.id}
            response = self.client.post('/api/rooms/create/', data)
            responses.append(response)
        
        # 所有请求都应该成功
        for response in responses:
            assert response.status_code == status.HTTP_201_CREATED
        
        # 检查房间代码是否唯一
        room_codes = [r.data['room_code'] for r in responses]
        assert len(set(room_codes)) == len(room_codes)  # 所有代码都应该是唯一的
    
    def test_room_state_transitions_via_api(self):
        """测试通过API进行房间状态转换"""
        # 创建房间
        self.authenticate_user(self.host)
        data = {'template_id': self.template.id}
        response = self.client.post('/api/rooms/create/', data)
        if response.status_code == status.HTTP_201_CREATED:
            room_code = response.data['room_code']

            # 获取房间详情，检查初始状态
            response = self.client.get(f'/api/rooms/{room_code}/')
            assert response.data['status'] == RoomState.OPEN
        
        # 尝试开始游戏（如果有相应的API端点）
        # 这里假设有一个开始游戏的端点
        # response = self.client.post(f'/api/rooms/{room_code}/start/')
        # assert response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND]


@pytest.mark.api
class TestTemplateAPIExtended:
    """扩展的模板API测试"""

    def setup_method(self):
        """设置测试数据"""
        self.client = APIClient()
        cache.clear()

        self.user = User.objects.create_user(
            username='test_template_user',
            password='testpass123',
            subscription_level='Pro'
        )

        self.free_user = User.objects.create_user(
            username='test_free_template_user',
            password='testpass123',
            subscription_level='Free'
        )

        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='Test System Template',
            description='System template for testing',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {'type': 'GAME_PICTIONARY', 'duration': 300}
                ]
            }
        )

        self.pro_system_template = SystemTemplate.objects.create(
            name='Pro System Template',
            description='Pro system template for testing',
            required_subscription=SystemTemplate.SUBSCRIPTION_PRO,
            template_config={
                'steps': [
                    {'type': 'POLL', 'duration': 300}
                ]
            }
        )

    def teardown_method(self):
        """清理测试数据"""
        SystemTemplate.objects.filter(name__contains='Test').delete()
        EventTemplate.objects.filter(name__contains='Test').delete()
        User.objects.filter(username__startswith='test_').delete()

    def authenticate_user(self, user):
        """认证用户"""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_get_templates_without_authentication(self):
        """测试未认证获取模板列表"""
        response = self.client.get('/api/events/templates/')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_get_templates_subscription_filtering(self):
        """测试模板列表按订阅等级过滤"""
        # Free用户应该只能看到Free模板
        self.authenticate_user(self.free_user)
        response = self.client.get('/api/events/templates/')

        if response.status_code == status.HTTP_200_OK:
            templates = response.data
            system_templates = [t for t in templates if t.get('type') == 'system']
            template_names = [t['name'] for t in system_templates]

            assert 'Test System Template' in template_names
            assert 'Pro System Template' not in template_names

        # Pro用户应该能看到所有模板
        self.authenticate_user(self.user)
        response = self.client.get('/api/events/templates/')

        if response.status_code == status.HTTP_200_OK:
            templates = response.data
            system_templates = [t for t in templates if t.get('type') == 'system']
            template_names = [t['name'] for t in system_templates]

            assert 'Test System Template' in template_names
            assert 'Pro System Template' in template_names

    def test_create_template_with_invalid_data(self):
        """测试使用无效数据创建模板"""
        self.authenticate_user(self.user)

        invalid_data_sets = [
            {},  # 空数据
            {'name': ''},  # 空名称
            {'name': 'Test', 'steps': 'invalid'},  # 无效步骤格式
            {'name': 'Test', 'steps': []},  # 空步骤列表
        ]

        for invalid_data in invalid_data_sets:
            response = self.client.post('/api/events/templates/', invalid_data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_create_template_with_premium_steps_insufficient_subscription(self):
        """测试Free用户创建包含高级步骤的模板"""
        self.authenticate_user(self.free_user)

        data = {
            'name': 'Premium Template',
            'description': 'Template with premium steps',
            'steps': [
                {
                    'name': 'Poll Step',
                    'step_type': EventStep.STEP_POLL,
                    'duration': 300,
                    'order': 1
                }
            ]
        }

        response = self.client.post('/api/events/templates/', data)
        # 应该失败或者创建时移除高级步骤
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_201_CREATED]

    def test_update_template_unauthorized(self):
        """测试未授权更新模板"""
        # 创建模板
        template = EventTemplate.objects.create(
            name='Update Test Template',
            creator=self.user
        )

        # 其他用户尝试更新
        self.authenticate_user(self.free_user)
        data = {'name': 'Updated Name'}
        response = self.client.patch(f'/api/events/templates/{template.id}/', data)

        assert response.status_code in [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND]

    def test_delete_template_unauthorized(self):
        """测试未授权删除模板"""
        # 创建模板
        template = EventTemplate.objects.create(
            name='Delete Test Template',
            creator=self.user
        )

        # 其他用户尝试删除
        self.authenticate_user(self.free_user)
        response = self.client.delete(f'/api/events/templates/{template.id}/')

        assert response.status_code in [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND]


@pytest.mark.api
class TestEventStepAPIExtended:
    """扩展的事件步骤API测试"""

    def setup_method(self):
        """设置测试数据"""
        self.client = APIClient()
        cache.clear()

        self.user = User.objects.create_user(
            username='test_step_user',
            password='testpass123',
            subscription_level='Pro'
        )

        self.template = EventTemplate.objects.create(
            name='Step Test Template',
            creator=self.user
        )

    def teardown_method(self):
        """清理测试数据"""
        EventTemplate.objects.filter(name__contains='Step Test').delete()
        User.objects.filter(username__startswith='test_').delete()

    def authenticate_user(self, user):
        """认证用户"""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_create_step_with_invalid_duration(self):
        """测试创建无效持续时间的步骤"""
        self.authenticate_user(self.user)

        invalid_durations = [
            -1,      # 负数
            0,       # 零
            'invalid',  # 非数字
            '',      # 空字符串
        ]

        for duration in invalid_durations:
            data = {
                'template': self.template.id,
                'order': 1,
                'step_type': EventStep.STEP_GAME_PICTIONARY,
                'duration': duration
            }
            response = self.client.post('/api/events/steps/', data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_create_step_with_invalid_order(self):
        """测试创建无效顺序的步骤"""
        self.authenticate_user(self.user)

        invalid_orders = [
            -1,      # 负数
            0,       # 零
            'invalid',  # 非数字
            '',      # 空字符串
        ]

        for order in invalid_orders:
            data = {
                'template': self.template.id,
                'order': order,
                'step_type': EventStep.STEP_GAME_PICTIONARY,
                'duration': 300
            }
            response = self.client.post('/api/events/steps/', data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_create_step_with_complex_configuration(self):
        """测试创建带复杂配置的步骤"""
        self.authenticate_user(self.user)

        complex_config = {
            'rounds': 3,
            'time_per_round': 120,
            'words': ['apple', 'banana', 'cherry'],
            'settings': {
                'allow_hints': True,
                'max_players': 8,
                'difficulty': 'medium'
            },
            'scoring': {
                'correct_guess': 10,
                'time_bonus': 5,
                'drawer_bonus': 3
            }
        }

        data = {
            'template': self.template.id,
            'order': 1,
            'step_type': EventStep.STEP_GAME_PICTIONARY,
            'duration': 600,
            'configuration': complex_config
        }

        response = self.client.post('/api/event-steps/', data)
        if response.status_code == status.HTTP_201_CREATED:
            # 验证配置是否正确保存
            step_id = response.data['id']
            step = EventStep.objects.get(id=step_id)
            assert step.configuration == complex_config

    def test_update_step_order_conflict(self):
        """测试更新步骤顺序冲突"""
        self.authenticate_user(self.user)

        # 创建两个步骤
        step1 = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )

        step2 = EventStep.objects.create(
            template=self.template,
            order=2,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=180
        )

        # 尝试将step2的顺序改为1（与step1冲突）
        data = {'order': 1}
        response = self.client.patch(f'/api/event-steps/{step2.id}/', data)

        # 应该处理冲突或返回错误
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
